<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护措施选一选</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 36px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            padding: 10px;
            border-radius: 10px;
        }
        
        .game-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 25px;
            width: 90%;
            max-width: 800px;
        }
        
        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
            border-radius: 10px;
            overflow: hidden;
        }
        
        th, td {
            border: none;
            padding: 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 18px;
            overflow: hidden;
        }
        
        th {
            background: linear-gradient(135deg, #4CAF50, #2196F3);
            color: white;
            font-weight: bold;
            font-size: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        th:first-child {
            width: 25%;
        }
        
        th:nth-child(2) {
            width: 25%;
        }
        
        th:last-child {
            width: 50%;
        }
        
        tr:nth-child(even) td {
            background-color: rgba(200, 255, 200, 0.2);
        }
        
        tr:nth-child(odd) td {
            background-color: rgba(200, 255, 200, 0.4);
        }
        
        .dropzone {
            min-height: 90px;
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 10px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            justify-content: center;
            align-items: center;
            width: 100%;
            box-sizing: border-box;
        }
        
        .dropzone.highlight {
            background-color: rgba(76, 175, 80, 0.2);
            border-color: #4CAF50;
            transform: scale(1.02);
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
        }
        
        .dropzone.empty {
            border-color: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        }
        
        .options-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
            perspective: 1000px;
        }
        
        .option {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            padding: 12px 18px;
            border-radius: 8px;
            cursor: grab;
            user-select: none;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
            font-weight: bold;
            position: relative;
            overflow: hidden;
            z-index: 1;
            font-size: 18px;
        }
        
        .option:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
            z-index: -1;
            transform: translateY(-100%);
            transition: transform 0.4s;
        }
        
        .option:hover {
            transform: translateY(-5px) rotateX(10deg);
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 24px rgba(31, 38, 135, 0.5);
        }
        
        .option:hover:before {
            transform: translateY(0);
        }
        
        .option.dragging {
            opacity: 0.7;
            transform: scale(1.1);
        }
        
        .dropped-option {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            padding: 6px 10px;
            border-radius: 6px;
            margin: 3px;
            display: flex;
            position: relative;
            box-shadow: 0 2px 8px rgba(31, 38, 135, 0.3);
            animation: dropIn 0.5s forwards;
            transform-origin: center;
            font-size: 16px;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-width: 60px;
            max-width: calc(100% - 10px);
            word-wrap: break-word;
            word-break: break-all;
            box-sizing: border-box;
            flex-shrink: 1;
            flex-grow: 0;
        }
        
        @keyframes dropIn {
            0% { transform: scale(0); opacity: 0; }
            70% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .dropped-option .remove-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            line-height: 18px;
            text-align: center;
            font-size: 12px;
            cursor: pointer;
            display: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            border: 2px solid white;
            transition: all 0.2s;
        }
        
        .dropped-option:hover .remove-btn {
            display: block;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .remove-btn:hover {
            background-color: #c0392b;
            transform: scale(1.1);
        }
        
        .buttons-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
        }
        
        .btn {
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            flex: 1;
            text-align: center;
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .home-btn {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
        
        .btn:active:not(:disabled) {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .tooltip {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 16px;
            max-width: 300px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            animation: tooltipFadeIn 0.3s forwards;
        }
        
        @keyframes tooltipFadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @media (max-width: 600px) {
            .game-container {
                padding: 15px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            th, td {
                padding: 10px;
                font-size: 16px;
            }
            
            .option {
                padding: 10px 14px;
                font-size: 16px;
            }
            
            .dropped-option {
                font-size: 14px;
                padding: 5px 8px;
            }
            
            .btn {
                padding: 10px 20px;
                font-size: 16px;
            }
            
            .buttons-container {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <h1>保护措施选一选</h1>
    
    <div class="game-container">
        <table>
            <thead>
                <tr>
                    <th>数据处理活动</th>
                    <th>具体行为</th>
                    <th>数据安全保护措施</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据收集</td>
                    <td>扫码、填信息等</td>
                    <td>
                        <div class="dropzone empty" data-activity="数据收集"></div>
                    </td>
                </tr>
                <tr>
                    <td>数据存储</td>
                    <td>丢票据、备份等</td>
                    <td>
                        <div class="dropzone empty" data-activity="数据存储"></div>
                    </td>
                </tr>
                <tr>
                    <td>数据共享</td>
                    <td>发朋友圈、问卷等</td>
                    <td>
                        <div class="dropzone empty" data-activity="数据共享"></div>
                    </td>
                </tr>
                <tr>
                    <td>数据传输</td>
                    <td>连公共WiFi、发文件等</td>
                    <td>
                        <div class="dropzone empty" data-activity="数据传输"></div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="options-container">
            <div class="option" draggable="true">使用自己的流量</div>
            <div class="option" draggable="true">定期备份在U盘</div>
            <div class="option" draggable="true">销毁</div>
            <div class="option" draggable="true">谁可以看</div>
            <div class="option" draggable="true">慎重填写</div>
            <div class="option" draggable="true">尽可能少提供信息</div>
        </div>
        
        <div class="buttons-container">
            <button class="btn reset-btn" onclick="resetAll()">重填</button>
            <button class="btn home-btn" onclick="goToHome()" disabled>回到主界面</button>
        </div>
    </div>

    <script>
        let draggedElement = null;
        let tooltip = null;
        // 为当前游戏创建一个唯一的本地存储键
        const STORAGE_KEY = 'data_security_game_state';
        
        document.addEventListener('DOMContentLoaded', function() {
            // 设置拖拽事件
            const options = document.querySelectorAll('.option');
            const dropzones = document.querySelectorAll('.dropzone');
            
            options.forEach(option => {
                option.addEventListener('dragstart', dragStart);
                option.addEventListener('dragend', dragEnd);
                
                // 添加鼠标悬停动画
                option.addEventListener('mouseover', function() {
                    this.style.transform = 'translateY(-5px) rotateX(10deg)';
                });
                
                option.addEventListener('mouseout', function() {
                    this.style.transform = '';
                });
            });
            
            dropzones.forEach(dropzone => {
                dropzone.addEventListener('dragover', dragOver);
                dropzone.addEventListener('dragenter', dragEnter);
                dropzone.addEventListener('dragleave', dragLeave);
                dropzone.addEventListener('drop', drop);
                // 初始时将所有放置区域标记为空
                dropzone.classList.add('empty');
            });
            
            // 初始化回到主界面按钮
            const homeBtn = document.querySelector('.home-btn');
            homeBtn.disabled = true;
            homeBtn.addEventListener('click', function(e) {
                // 如果按钮被禁用，显示提示信息
                if (this.disabled) {
                    e.preventDefault();
                    showTooltip(this, '请确保每个数据处理活动都至少放置一个保护措施！');
                    return false;
                }
                goToHome();
            });
            
            // 尝试加载保存的状态
            loadSavedState();
            
            // 添加初始动画效果
            animateOptions();
        });
        
        // 保存当前所有放置区域的状态到本地存储
        function saveCurrentState() {
            const dropzones = document.querySelectorAll('.dropzone');
            const state = {};
            
            dropzones.forEach(dropzone => {
                const activity = dropzone.getAttribute('data-activity');
                const options = [];
                
                dropzone.querySelectorAll('.dropped-option').forEach(option => {
                    // 排除删除按钮，只获取文本内容
                    options.push(option.childNodes[0].nodeValue);
                });
                
                state[activity] = options;
            });
            
            // 保存到本地存储
            localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
        }
        
        // 从本地存储加载保存的状态
        function loadSavedState() {
            const savedState = localStorage.getItem(STORAGE_KEY);
            if (!savedState) return;
            
            try {
                const state = JSON.parse(savedState);
                const dropzones = document.querySelectorAll('.dropzone');
                
                dropzones.forEach(dropzone => {
                    const activity = dropzone.getAttribute('data-activity');
                    
                    if (state[activity] && state[activity].length > 0) {
                        state[activity].forEach(optionText => {
                            // 创建并添加选项
                            createDroppedOption(dropzone, optionText);
                        });
                    }
                });
                
                // 检查所有放置区是否已填满
                checkAllDropzonesHaveItems();
                
            } catch (e) {
                console.error('加载保存的状态时出错:', e);
                // 如果出错，清除可能损坏的状态
                localStorage.removeItem(STORAGE_KEY);
            }
        }
        
        // 创建已放置的选项元素
        function createDroppedOption(dropzone, optionText) {
            const droppedOption = document.createElement('div');
            droppedOption.className = 'dropped-option';
            droppedOption.innerText = optionText;
            
            // 根据内容长度自动调整大小
            const textLength = optionText.length;
            if (textLength <= 4) {
                droppedOption.style.fontSize = '16px';
                droppedOption.style.minWidth = '60px';
            } else if (textLength <= 8) {
                droppedOption.style.fontSize = '15px';
                droppedOption.style.minWidth = '90px';
            } else {
                droppedOption.style.fontSize = '14px';
                droppedOption.style.minWidth = '120px';
            }
            
            // 添加删除按钮
            const removeBtn = document.createElement('span');
            removeBtn.className = 'remove-btn';
            removeBtn.innerText = '×';
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const parent = droppedOption.parentNode;
                
                // 添加删除动画
                droppedOption.style.transform = 'scale(0)';
                droppedOption.style.opacity = '0';
                
                setTimeout(() => {
                    if (parent && droppedOption.parentNode === parent) {
                        parent.removeChild(droppedOption);
                        // 检查所有放置区
                        checkAllDropzonesHaveItems();
                        // 保存当前状态
                        saveCurrentState();
                    }
                }, 300);
            });
            
            droppedOption.appendChild(removeBtn);
            dropzone.appendChild(droppedOption);
        }
        
        function showTooltip(element, message) {
            // 如果已有提示框，先移除
            if (tooltip) {
                document.body.removeChild(tooltip);
            }
            
            // 创建提示框
            tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = message;
            
            // 计算位置
            const rect = element.getBoundingClientRect();
            tooltip.style.top = (rect.top - 50) + 'px';
            tooltip.style.left = (rect.left + rect.width / 2 - 150) + 'px';
            
            document.body.appendChild(tooltip);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (tooltip && tooltip.parentNode) {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (tooltip && tooltip.parentNode) {
                            document.body.removeChild(tooltip);
                            tooltip = null;
                        }
                    }, 300);
                }
            }, 3000);
        }
        
        function checkAllDropzonesHaveItems() {
            const dropzones = document.querySelectorAll('.dropzone');
            let allFilled = true;
            
            dropzones.forEach(dropzone => {
                // 检查每个放置区是否有内容
                const hasItems = dropzone.querySelectorAll('.dropped-option').length > 0;
                
                // 更新放置区状态
                if (hasItems) {
                    dropzone.classList.remove('empty');
                } else {
                    dropzone.classList.add('empty');
                    allFilled = false;
                }
            });
            
            // 更新按钮状态
            const homeBtn = document.querySelector('.home-btn');
            homeBtn.disabled = !allFilled;
            
            return allFilled;
        }
        
        function animateOptions() {
            const options = document.querySelectorAll('.option');
            options.forEach((option, index) => {
                option.style.opacity = '0';
                option.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    option.style.transition = 'all 0.5s ease';
                    option.style.opacity = '1';
                    option.style.transform = '';
                }, 100 * index);
            });
        }
        
        function dragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.setData('text/plain', this.innerText);
            
            // 创建拖动时的视觉效果
            setTimeout(() => {
                this.style.opacity = '0.4';
            }, 0);
        }
        
        function dragEnd() {
            this.classList.remove('dragging');
            this.style.opacity = '1';
        }
        
        function dragOver(e) {
            e.preventDefault();
        }
        
        function dragEnter(e) {
            e.preventDefault();
            this.classList.add('highlight');
        }
        
        function dragLeave() {
            this.classList.remove('highlight');
        }
        
        function drop(e) {
            e.preventDefault();
            this.classList.remove('highlight');
            
            const optionText = e.dataTransfer.getData('text/plain');
            
            // 创建新的已放置选项元素
            createDroppedOption(this, optionText);
            
            // 添加放置动画效果
            const droppedOption = this.querySelector('.dropped-option:last-child');
            droppedOption.style.transform = 'scale(0)';
            droppedOption.style.opacity = '0';
            
            setTimeout(() => {
                droppedOption.style.transform = 'scale(1)';
                droppedOption.style.opacity = '1';
                // 检查所有放置区
                checkAllDropzonesHaveItems();
                // 保存当前状态
                saveCurrentState();
            }, 10);
        }
        
        function resetAll() {
            // 清空所有放置区域
            const dropzones = document.querySelectorAll('.dropzone');
            
            // 为每个dropzone添加消失动画
            dropzones.forEach(dropzone => {
                const droppedOptions = dropzone.querySelectorAll('.dropped-option');
                
                droppedOptions.forEach((option, index) => {
                    // 添加延迟，使动画看起来更有序列感
                    setTimeout(() => {
                        option.style.transform = 'scale(0)';
                        option.style.opacity = '0';
                        
                        setTimeout(() => {
                            if (option.parentNode) {
                                option.parentNode.removeChild(option);
                            }
                        }, 300);
                    }, index * 50);
                });
                
                // 重新标记为空
                setTimeout(() => {
                    dropzone.classList.add('empty');
                }, 350);
            });
            
            // 重置按钮动画
            const resetBtn = document.querySelector('.reset-btn');
            resetBtn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                resetBtn.style.transform = '';
            }, 500);
            
            // 禁用回到主界面按钮
            const homeBtn = document.querySelector('.home-btn');
            homeBtn.disabled = true;
            
            // 清除保存的状态
            localStorage.removeItem(STORAGE_KEY);
        }
        
        function goToHome() {
            // 再次检查所有放置区是否都有内容
            if (!checkAllDropzonesHaveItems()) {
                showTooltip(document.querySelector('.home-btn'), '请确保每个数据处理活动都至少放置一个保护措施！');
                return;
            }
            
            // 保存当前状态
            saveCurrentState();
            
            // 设置第四关完成标记
            localStorage.setItem('level4_completed', 'true');
            // 直接跳转回主页，无需等待
            window.location.href = '../数据安全小卫士闯关乐园.html';
        }
        
        // 初始化AI助手
        function initAI() {
            // 引入AI助手脚本
            const aiScript = document.createElement('script');
            aiScript.src = '../ai-assistant.js';
            aiScript.onload = function() {
                const configScript = document.createElement('script');
                configScript.src = '../ai-config.js';
                configScript.onload = function() {
                    // 初始化AI助手
                    const assistant = window.initPageAIAssistant({
                        context: '数据保护措施匹配练习',
                        avatar: '../素材/5.png',
                        name: '保护小专家'
                    });
                    
                    window.aiAssistant = assistant;
                    
                    // 监听拖拽操作，提供指导
                    const dropzones = document.querySelectorAll('.dropzone');
                    dropzones.forEach(dropzone => {
                        dropzone.addEventListener('drop', function() {
                            setTimeout(() => {
                                const activity = this.getAttribute('data-activity');
                                let tip = '';
                                switch(activity) {
                                    case '数据收集':
                                        tip = '收集数据时要谨慎，只提供必要的信息！';
                                        break;
                                    case '数据存储':
                                        tip = '存储数据要选择安全的地方，定期清理不需要的文件！';
                                        break;
                                    case '数据共享':
                                        tip = '分享前要想想"谁可以看"，保护好隐私！';
                                        break;
                                    case '数据传输':
                                        tip = '传输数据时要用安全的网络，避免公共WiFi！';
                                        break;
                                }
                                if (tip) {
                                    assistant.addMessage(`💡 ${tip}`, 'assistant');
                                }
                            }, 500);
                        });
                    });
                    
                    // 完成所有匹配时的祝贺
                    const homeBtn = document.querySelector('.home-btn');
                    if (homeBtn) {
                        homeBtn.addEventListener('click', function() {
                            if (!this.disabled) {
                                setTimeout(() => {
                                    assistant.addMessage('🎉 恭喜你掌握了各种数据保护措施！你已经是数据安全小专家了！', 'assistant');
                                    assistant.showNotification();
                                }, 500);
                            }
                        });
                    }
                };
                document.head.appendChild(configScript);
            };
            document.head.appendChild(aiScript);
        }
        
        // 页面加载完成后初始化AI助手
        setTimeout(initAI, 500);
    </script>
</body>
</html> 