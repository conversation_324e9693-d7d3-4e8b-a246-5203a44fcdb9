<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分享试一试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            color: #333;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .page-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            width: 100%;
            max-width: 1200px;
            position: relative;
            margin: 0 auto;
        }

        .container {
            max-width: 500px;
            width: 100%;
            margin: 0;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 90vh;
            max-height: 700px;
            display: flex;
            flex-direction: column;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            overflow: hidden;
            flex: 1;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header-title {
            font-size: 17px;
            font-weight: 500;
            color: #333;
        }

        .cancel-btn {
            color: #888;
            border: none;
            background: none;
            font-size: 16px;
            cursor: pointer;
            transition: color 0.3s;
        }

        .cancel-btn:hover {
            color: #555;
        }

        .publish-btn {
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 6px 18px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .publish-btn:hover {
            background-color: #06ad56;
        }

        .publish-btn:disabled {
            background-color: #9fd6b8;
            cursor: not-allowed;
        }

        .button-enabled {
            animation: pulse 0.3s;
        }

        .button-clicked {
            transform: scale(0.95);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            position: relative;
        }

        textarea {
            width: 100%;
            min-height: 120px;
            border: none;
            resize: none;
            font-size: 16px;
            outline: none;
            margin-bottom: 20px;
            line-height: 1.5;
            transition: height 0.2s;
        }

        .image-upload-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        #preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .image-preview {
            width: 90px;
            height: 90px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, opacity 0.2s;
            cursor: pointer;
        }

        .image-preview:hover {
            transform: scale(1.03);
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-preview .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s, transform 0.2s;
        }

        .image-preview .remove-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .image-upload {
            width: 90px;
            height: 90px;
            border: 1px dashed #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s, transform 0.2s;
            position: relative;
            overflow: hidden;
        }

        .image-upload:hover {
            background-color: #f9f9f9;
            border-color: #ccc;
            transform: scale(1.03);
        }

        .image-upload:active {
            transform: scale(0.97);
        }

        .image-upload input {
            display: none;
        }

        .image-upload i {
            font-size: 24px;
            color: #888;
            transition: color 0.3s;
            margin-bottom: 5px;
        }
        
        .image-upload .upload-text {
            font-size: 10px;
            color: #888;
            text-align: center;
            transition: color 0.3s;
            margin-top: 2px;
        }

        .image-upload:hover i,
        .image-upload:hover .upload-text {
            color: #07c160;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .location-selector, .visibility-selector {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #576b95;
            padding: 8px 0;
            position: relative;
            transition: background-color 0.3s;
            border-radius: 8px;
            padding-left: 8px;
        }

        .location-selector:hover, .visibility-selector:hover {
            background-color: #f0f0f0;
        }

        .location-selector i, .visibility-selector i {
            margin-right: 12px;
            width: 20px;
            font-size: 18px;
        }

        .location-selector span, .visibility-selector span {
            margin-right: 10px;
            font-size: 15px;
        }

        .arrow-icon {
            margin-right: 0;
            width: auto;
            font-size: 14px;
            color: #ccc;
            position: absolute;
            right: 8px;
            transition: transform 0.3s;
        }

        .location-selector:hover .arrow-icon, 
        .visibility-selector:hover .arrow-icon {
            transform: translateX(3px);
            color: #999;
        }

        select {
            border: none;
            background: none;
            color: #576b95;
            outline: none;
            font-size: 15px;
            flex: 1;
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }

        /* 拖拽样式 */
        .drag-over {
            background-color: rgba(7, 193, 96, 0.08);
            border: 2px dashed #07c160;
            border-radius: 12px;
            animation: pulse-border 1.5s infinite;
        }

        @keyframes pulse-border {
            0% { border-color: rgba(7, 193, 96, 0.5); }
            50% { border-color: rgba(7, 193, 96, 1); }
            100% { border-color: rgba(7, 193, 96, 0.5); }
        }

        /* 添加拖拽目标区域样式 */
        .content {
            position: relative;
        }

        .drag-target-hint {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(7, 193, 96, 0.05);
            border: 3px dashed rgba(7, 193, 96, 0.3);
            border-radius: 12px;
            justify-content: center;
            align-items: center;
            z-index: 10;
            pointer-events: none;
        }

        .drag-target-hint-text {
            background-color: rgba(7, 193, 96, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .container.drag-over .drag-target-hint {
            display: flex;
            animation: fade-in 0.3s forwards;
        }

        @keyframes fade-in {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式调整 */
        @media (max-height: 700px) {
            .container, .album-panel {
                height: 95vh;
            }
        }

        @media (max-width: 1100px) {
            .page-container {
                flex-direction: column;
                align-items: center;
            }
            
            .album-panel {
                position: relative;
                top: 0;
                transform: none;
                width: 100%;
                max-width: 500px;
                margin-top: 20px;
                margin-bottom: 20px;
                height: auto;
                max-height: 500px;
            }
            
            .album-panel.visible {
                transform: none;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 0;
            }
            
            .container, .album-panel {
                max-width: 100%;
                height: 100vh;
                max-height: none;
                border-radius: 0;
                box-shadow: none;
            }
            
            .album-panel {
                margin-top: 0;
                margin-bottom: 0;
            }
        }

        /* 添加朋友圈展示样式 */
        .moments-view {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            z-index: 1500;
            display: none;
            flex-direction: column;
            align-items: center;
            overflow-y: auto;
        }

        .moments-header {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            width: 100%;
            z-index: 10;
        }

        .moments-content {
            max-width: 500px;
            width: 100%;
            padding: 0 10px;
            flex: 1;
        }

        .back-btn {
            color: #576b95;
            border: none;
            background: none;
            font-size: 16px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .moments-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 17px;
            font-weight: 500;
        }

        .moment-item {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 12px 0;
            padding: 16px;
            animation: slideIn 0.3s ease-out;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .moment-user {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            margin-right: 10px;
            background: #f0f0f0;
        }

        .user-name {
            font-weight: 500;
            color: #576b95;
        }

        .moment-content {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 12px;
            color: #333;
        }

        .moment-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 12px;
        }

        .moment-images.single {
            display: block;
        }

        .moment-images img {
            width: 100%;
            aspect-ratio: 1;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .moment-images img:hover {
            transform: scale(1.02);
        }

        .moment-images.single img {
            width: auto;
            max-width: 100%;
            max-height: 300px;
            aspect-ratio: auto;
        }

        .moment-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #999;
            font-size: 14px;
        }

        .moment-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .edit-btn {
            color: #576b95;
            border: none;
            background: none;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .edit-btn:hover {
            background-color: rgba(87, 107, 149, 0.1);
        }

        .edit-btn i {
            font-size: 12px;
        }

        .moment-location {
            color: #576b95;
        }

        .moment-time {
            color: #999;
        }

        .moment-privacy {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #999;
            font-size: 14px;
        }

        .moment-privacy i {
            font-size: 12px;
        }

        /* 删除隐藏按钮的样式 */
        .hidden-complete-btn {
            display: none;
        }

        /* 修改完成按钮样式，使其更透明 */
        .complete-btn {
            position: absolute;
            right: 15px;
            color: rgba(87, 107, 149, 0.6);
            border: none;
            background: none;
            font-size: 16px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .complete-btn:active {
            opacity: 0.7;
        }

        /* 添加陌生人查看选项的样式 */
        .stranger-view-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-top: 1px solid #eee;
            margin-top: 5px;
        }

        .stranger-view-left {
            display: flex;
            align-items: center;
            color: #333;
        }

        .stranger-view-left i {
            margin-right: 12px;
            width: 20px;
            font-size: 18px;
            color: #576b95;
        }

        .stranger-view-desc {
            font-size: 15px;
        }

        .stranger-view-note {
            font-size: 12px;
            color: #999;
            margin-top: 3px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #07c160;
            transition: .3s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #07c160;
        }

        input:not(:checked) + .toggle-slider {
            background-color: #ccc;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }

        /* 添加新华网朋友圈样式 */
        .news-moment {
            margin-top: 15px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .news-moment:hover {
            background-color: #fafafa;
        }

        .news-moment:active {
            background-color: #f5f5f5;
        }

        .news-moment-header {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f5;
        }

        .news-avatar {
            width: 45px;
            height: 45px;
            border-radius: 5px;
            margin-right: 12px;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .news-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .news-info {
            flex: 1;
        }

        .news-name {
            font-weight: 500;
            color: #333;
            font-size: 16px;
            margin-bottom: 3px;
        }

        .news-name .verified {
            color: #07c160;
            font-size: 14px;
            margin-left: 5px;
        }

        .news-time {
            color: #999;
            font-size: 13px;
        }

        .news-content {
            padding: 16px;
        }

        .news-title {
            font-size: 17px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .news-desc {
            color: #666;
            font-size: 15px;
            line-height: 1.5;
        }

        .news-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
            margin-top: 12px;
            border-radius: 4px;
        }

        .news-footer {
            display: flex;
            justify-content: space-between;
            padding: 12px 16px;
            border-top: 1px solid #f5f5f5;
            color: #576b95;
            font-size: 14px;
        }

        .news-footer .action {
            display: flex;
            align-items: center;
        }

        .news-footer .action i {
            margin-right: 5px;
        }
        
        /* 添加模态弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 3000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            width: 85%;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalIn 0.5s ease-out forwards;
            transform: translateY(-50px);
            opacity: 0;
        }
        
        @keyframes modalIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-title {
            color: #07c160;
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .modal-text {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
            line-height: 1.5;
        }
        
        .shield-container {
            margin: 20px auto;
            width: 100px;
            height: 100px;
            position: relative;
        }
        
        .security-shield {
            position: relative;
            width: 100px;
            height: 100px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2307c160"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0;
            filter: drop-shadow(0 0 10px rgba(7, 193, 96, 0.7));
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }
        
        .show-shield {
            opacity: 1;
            animation: fadeIn 1s ease-in-out forwards, float 3s ease-in-out infinite;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 相册面板样式 */
        .album-panel {
            width: 350px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            height: 90vh;
            max-height: 700px;
            position: sticky;
            top: 20px;
            transition: all 0.3s ease;
            z-index: 1000;
            transform: translateX(20px);
            opacity: 0;
            visibility: hidden;
            flex-shrink: 0;
        }
        
        .album-panel.visible {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }

        .album-header {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .album-title {
            font-size: 17px;
            font-weight: 500;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .album-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-select-btn, .close-album-btn {
            background: none;
            border: none;
            color: #576b95;
            font-size: 18px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .file-select-btn:hover, .close-album-btn:hover {
            background-color: rgba(87, 107, 149, 0.1);
        }

        .album-content {
            padding: 15px;
            flex: 1;
            overflow-y: auto;
        }

        .album-images {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: space-between;
        }

        .album-image-item {
            width: 120px;
            height: 120px;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            cursor: grab;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .album-image-item:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
        }
        
        .album-image-item:active {
            cursor: grabbing;
            transform: scale(0.98);
        }

        .album-image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* 图片容器样式 */
        .image-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            border-radius: 10px;
        }
        
        /* 拖拽状态样式 */
        .album-image-item.dragging {
            opacity: 0.7;
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            z-index: 10;
            cursor: grabbing;
        }

        /* 添加拖拽提示样式 */
        .drag-hint {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 5px;
            background-color: rgba(7, 193, 96, 0.8);
            color: white;
            font-size: 12px;
            text-align: center;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            opacity: 0;
            transform: translateY(100%);
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .album-image-item:hover .drag-hint {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 已添加标记样式 */
        .added-mark {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(7, 193, 96, 0.5);
            color: white;
            font-size: 20px;
            font-weight: bold;
            z-index: 5;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            animation: pulse-mark 2s ease-in-out infinite;
            pointer-events: none; /* 确保点击事件可以穿透到下方图片 */
        }
        
        /* 相册图片项中的已添加标记样式 */
        .image-container .added-mark {
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 0; /* 与图片容器内部不需要圆角 */
            backdrop-filter: blur(1px); /* 添加轻微模糊效果 */
            box-shadow: inset 0 0 30px rgba(7, 193, 96, 0.3); /* 内部阴影效果 */
        }
        
        @keyframes pulse-mark {
            0% { background-color: rgba(7, 193, 96, 0.5); }
            50% { background-color: rgba(7, 193, 96, 0.7); }
            100% { background-color: rgba(7, 193, 96, 0.5); }
        }
        
        .added-mark i {
            margin-right: 8px;
            animation: bounce 1s ease infinite;
            font-size: 22px; /* 增大图标 */
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3)); /* 添加图标阴影 */
        }
        
        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .already-added {
            opacity: 0.95;
            transform: scale(1);
            transition: transform 0.3s;
        }
        
        .already-added:hover {
            opacity: 1;
            transform: scale(1.05);
        }
        
        .already-added::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(7, 193, 96, 0.1);
            pointer-events: none;
            border-radius: 10px;
            border: 2px solid rgba(7, 193, 96, 0.5);
        }
    </style>
</head>
<body>
    <div class="page-container">
    <div class="container">
        <div class="header">
            <button class="cancel-btn">取消</button>
            <span class="header-title">发表朋友圈</span>
            <button class="publish-btn">发表</button>
        </div>
        
        <div class="content">
            <textarea placeholder="这一刻的想法..."></textarea>
            
            <div class="image-upload-container">
                <div id="preview-container"></div>
                    <div class="image-upload" title="点击添加图片或查看相册" id="image-upload-btn">
                    <input type="file" id="image-input" multiple accept="image/*" style="display: none;">
                    <!-- 移除label，让点击相机图标默认触发相册面板 -->
                        <i class="fas fa-camera"></i>
                    <span class="upload-text">相册</span>
                </div>
            </div>
            
            <!-- 添加拖拽提示区域 -->
            <div class="drag-target-hint">
                <div class="drag-target-hint-text">
                    <i class="fas fa-arrow-down" style="margin-right: 8px;"></i>拖拽照片到这里
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="location-selector">
                <i class="fas fa-map-marker-alt"></i>
                <span>所在位置</span>
                <select id="location">
                    <option value="">不显示位置</option>
                    <option value="北京市">北京市</option>
                    <option value="上海市">上海市</option>
                    <option value="广州市">广州市</option>
                    <option value="深圳市">深圳市</option>
                    <option value="杭州市">杭州市</option>
                    <option value="苏州市">苏州市</option>
                </select>
                <i class="fas fa-chevron-right arrow-icon"></i>
            </div>
            
            <div class="visibility-selector">
                <i class="fas fa-eye"></i>
                <span>谁可以看</span>
                <select id="visibility">
                    <option value="public">公开</option>
                    <option value="friends">仅朋友</option>
                    <option value="private">私密</option>
                    <option value="custom">部分可见</option>
                </select>
                <i class="fas fa-chevron-right arrow-icon"></i>
            </div>
            
            <div class="stranger-view-option">
                <div class="stranger-view-left">
                    <i class="fas fa-user-friends"></i>
                    <div>
                        <div class="stranger-view-desc">允许陌生人查看十条朋友圈</div>
                        <div class="stranger-view-note">开启后，陌生人可查看你最近的十条朋友圈</div>
                    </div>
                </div>
                <label class="toggle-switch">
                    <input type="checkbox" id="stranger-view-toggle" checked>
                    <span class="toggle-slider"></span>
                </label>
                </div>
            </div>
        </div>
        
        <!-- 添加相册面板 -->
        <div class="album-panel" id="album-panel">
            <div class="album-header">
                <div class="album-title">
                    <span>小智的相册</span>
                </div>
                <div class="album-actions">
                    <button class="file-select-btn" id="file-select-btn" title="从本地选择图片">
                        <i class="fas fa-folder-open"></i>
                    </button>
                    <button class="close-album-btn" id="close-album-btn" title="关闭相册">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="album-content">
                <div class="album-images" id="album-images">
                    <!-- 相册图片将通过JavaScript动态添加 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 修改朋友圈展示界面，在标题栏添加完成按钮 -->
    <div class="moments-view">
        <div class="moments-header">
            <button class="back-btn">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="moments-title">朋友圈</span>
            <button class="complete-btn">完成</button>
        </div>
        <div class="moments-content">
            <div class="moment-item">
                <div class="moment-user">
                    <div class="user-avatar">
                        <i class="fas fa-user" style="font-size: 24px; color: #999; display: flex; justify-content: center; align-items: center; height: 100%;"></i>
                    </div>
                    <span class="user-name">我</span>
                </div>
                <div class="moment-content"></div>
                <div class="moment-images"></div>
                <div class="moment-meta">
                    <div class="moment-info">
                        <span class="moment-time">刚刚</span>
                        <span class="moment-location"></span>
                    </div>
                    <div class="moment-actions">
                        <button class="edit-btn">
                            <i class="fas fa-edit"></i>
                            <span>编辑</span>
                        </button>
                        <div class="moment-privacy">
                            <i class="fas fa-eye"></i>
                            <span class="privacy-text"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 添加分割线和安全提示标题 -->
            <div style="margin: 30px 0 20px; text-align: center; position: relative;">
                <hr style="border: none; border-top: 2px solid #ddd; margin: 0;" />
                <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background-color: #f5f5f5; padding: 0 15px; color: #576b95; font-size: 15px; font-weight: 500;">
                    发布朋友圈安全提示
                </div>
            </div>
            
            <!-- 添加新华网朋友圈，添加点击链接 -->
            <div class="news-moment" id="news-article-link">
                <div class="news-moment-header">
                    <div class="news-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzE5NDFBMiI+PHBhdGggZD0iTTEyIDJDNi40NzcgMiAyIDYuNDc3IDIgMTJzNC40NzcgMTAgMTAgMTAgMTAtNC40NzcgMTAtMTBTMTcuNTIzIDIgMTIgMnptLTEgMTVoMnYtNmgtMnY2em0wLThoMnYtMmgtMnYyeiIvPjwvc3ZnPg==" alt="新华网" style="background-color: #fff;">
                    </div>
                    <div class="news-info">
                        <div class="news-name">新华网 <span class="verified"><i class="fas fa-check-circle"></i></span></div>
                        <div class="news-time">2025年5月30日</div>
                    </div>
                </div>
                <div class="news-content">
                    <div class="news-title">朋友圈这些内容，千万不能发</div>
                    <div class="news-desc">端午假期即将来临，你有什么出行计划吗？一到假期，发朋友圈分享快乐的心蠢蠢欲动。然而，过度分享可能会带来隐私泄露的风险！
                        哪些内容不能随便晒？如何守护好个人信息安全？一起来看↓↓↓...</div>
                    <img class="news-image" src="../素材/重要提醒.webp" alt="朋友圈安全提示">
                </div>
                <div class="news-footer">
                    <div class="action"><i class="far fa-thumbs-up"></i> 点赞</div>
                    <div class="action"><i class="far fa-comment"></i> 评论</div>
                    <div class="action"><i class="fas fa-share"></i> 分享</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.message, 'at', e.filename, ':', e.lineno, ':', e.colno);
        });
        
        // 捕获未处理的Promise异常
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise异常:', e.reason);
        });
        
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.querySelector('textarea');
            const publishBtn = document.querySelector('.publish-btn');
            const cancelBtn = document.querySelector('.cancel-btn');
            const imageInput = document.getElementById('image-input');
            const previewContainer = document.getElementById('preview-container');
            const container = document.querySelector('.container');
            const locationSelect = document.getElementById('location');
            const visibilitySelect = document.getElementById('visibility');
            // 相册相关变量
            const albumPanel = document.getElementById('album-panel');
            const albumImages = document.getElementById('album-images');
            const imageUploadBtn = document.getElementById('image-upload-btn');
            const closeAlbumBtn = document.getElementById('close-album-btn');
            const momentsView = document.querySelector('.moments-view');
            
            // 创建数组保存已添加图片的ID或路径
            const addedImages = new Set();
            
            // 更新相册图片数据 - 使用实际的图片文件名
            const albumPhotos = [
                { id: 1, src: '../小智的相册/飞机票.jpg', alt: '飞机票' },
                { id: 2, src: '../小智的相册/酒店.jpeg', alt: '酒店' },
                { id: 3, src: '../小智的相册/自拍.png', alt: '自拍' },
                { id: 4, src: '../小智的相册/美食.jpg', alt: '美食' },
                { id: 5, src: '../小智的相册/游泳.jpg', alt: '游泳' },
                { id: 6, src: '../小智的相册/海边风景.jpg', alt: '海边风景' },
                { id: 7, src: '../小智的相册/沙滩.jpg', alt: '沙滩' },
                { id: 8, src: '../小智的相册/护照.png', alt: '护照' }
            ];
            
            // 保存朋友圈数据到localStorage的函数
            function saveMomentData() {
                const content = momentsView.dataset.content || '';
                const location = momentsView.dataset.location || '';
                const visibility = momentsView.dataset.visibility || 'public';
                const allowStrangerView = momentsView.dataset.allowStrangerView === 'true';
                
                // 获取图片URL
                let images = [];
                try {
                    images = JSON.parse(momentsView.dataset.images || '[]');
                } catch (e) {
                    console.error('解析图片数据失败:', e);
                    images = [];
                }
                
                // 创建要保存的数据对象
                const momentData = {
                    content,
                    location,
                    visibility,
                    allowStrangerView,
                    images,
                    timestamp: new Date().toISOString()
                };
                
                // 保存到localStorage
                try {
                    localStorage.setItem('savedMomentData', JSON.stringify(momentData));
                    console.log('朋友圈数据已保存到localStorage');
                } catch (e) {
                    console.error('保存朋友圈数据失败:', e);
                }
            }
            
            // 清除不需要的默认内容
            function clearUnwantedDefaultContent() {
                try {
                    const savedDataStr = localStorage.getItem('savedMomentData');
                    if (savedDataStr) {
                        const savedData = JSON.parse(savedDataStr);
                        // 如果保存的内容是"今天是个好日子"，则清除
                        if (savedData.content === '今天是个好日子') {
                            localStorage.removeItem('savedMomentData');
                            console.log('已清除默认内容："今天是个好日子"');
                            return true;
                        }
                    }
                } catch (e) {
                    console.error('清除默认内容时出错:', e);
                }
                return false;
            }

            // 检查并恢复保存的朋友圈数据到编辑界面
            function checkSavedMomentData() {
                try {
                    const savedDataStr = localStorage.getItem('savedMomentData');
                    if (!savedDataStr) return false;
                    
                    const savedData = JSON.parse(savedDataStr);
                    if (!savedData) return false;
                    
                    // 如果是不想要的默认内容，直接返回false
                    if (savedData.content === '今天是个好日子') {
                        localStorage.removeItem('savedMomentData');
                        console.log('已清除并跳过默认内容："今天是个好日子"');
                        return false;
                    }
                    
                    // 恢复到编辑界面，而不是显示已发布状态
                    textarea.value = savedData.content || '';
                    textarea.style.height = 'auto';
                    textarea.style.height = textarea.scrollHeight + 'px';
                    
                    locationSelect.value = savedData.location || '';
                    visibilitySelect.value = savedData.visibility || 'public';
                    strangerViewToggle.checked = savedData.allowStrangerView || false;
                    
                    // 恢复图片到编辑界面
                    if (savedData.images && savedData.images.length > 0) {
                        savedData.images.forEach(imageSrc => {
                            createImagePreview(imageSrc);
                        });
                    }
                    
                    // 启用发布按钮
                    checkEnablePublish();
                    
                    console.log('页面刷新后恢复编辑界面，包含之前的内容');
                    return true;
                } catch (e) {
                    console.error('恢复朋友圈数据失败:', e);
                    return false;
                }
            }
            
            // 加载相册图片
            function loadAlbumImages() {
                albumImages.innerHTML = '';
                
                // 添加调试信息
                console.log('加载相册图片，当前已添加图片集合:', Array.from(addedImages));
                
                albumPhotos.forEach(photo => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'album-image-item';
                    imageItem.setAttribute('draggable', 'true');
                    imageItem.dataset.id = photo.id;
                    
                    const img = document.createElement('img');
                    img.src = photo.src;
                    img.alt = photo.alt;
                    
                    // 添加错误处理，显示替代内容
                    img.onerror = function() {
                        this.onerror = null;
                        this.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="80" height="80"><path fill="%23999" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.12 2.12-1.41-1.41-1.41 1.41 2.83 2.83 3.54-3.54-1.43-1.41z"/></svg>';
                        this.style.padding = '5px';
                        this.style.background = '#f0f0f0';
                        
                        // 添加文本显示
                        const textDiv = document.createElement('div');
                        textDiv.textContent = photo.alt;
                        textDiv.style.fontSize = '10px';
                        textDiv.style.textAlign = 'center';
                        textDiv.style.color = '#666';
                        textDiv.style.padding = '2px';
                        textDiv.style.overflow = 'hidden';
                        textDiv.style.textOverflow = 'ellipsis';
                        textDiv.style.whiteSpace = 'nowrap';
                        imageItem.appendChild(textDiv);
                    };
                    
                    // 创建一个图片容器，用于定位已添加标记
                    const imgContainer = document.createElement('div');
                    imgContainer.className = 'image-container';
                    imgContainer.style.position = 'relative';
                    imgContainer.style.width = '100%';
                    imgContainer.style.height = '100%';
                    imgContainer.appendChild(img);
                    imageItem.appendChild(imgContainer);
                    
                    // 检查是否已添加该图片 - 更全面的检查
                    const isAdded = 
                        addedImages.has(photo.id) || 
                        addedImages.has(photo.src) || 
                        Array.from(addedImages).some(item => 
                            typeof item === 'string' && 
                            (item.includes(photo.alt) || 
                             item.includes(photo.src.split('/').pop()))
                        );
                    
                    // 添加已添加标记 - 直接附加到图片容器上
                    if (isAdded) {
                        console.log(`图片已标记为已添加 (在相册中): ID=${photo.id}, 名称=${photo.alt}`);
                        const addedMark = document.createElement('div');
                        addedMark.className = 'added-mark';
                        addedMark.innerHTML = '<i class="fas fa-check-circle"></i> 已添加';
                        imgContainer.appendChild(addedMark); // 修改为附加到图片容器上
                        imageItem.classList.add('already-added');
                        
                        // 禁用拖拽，避免重复添加
                        imageItem.setAttribute('draggable', 'false');
                    }
                    
                    // 添加拖拽提示
                    const dragHint = document.createElement('div');
                    dragHint.className = 'drag-hint';
                    
                    // 根据是否已添加修改提示文本
                    dragHint.textContent = isAdded ? '已添加到朋友圈' : '拖拽到朋友圈';
                    imageItem.appendChild(dragHint);
                    
                    // 添加到相册面板
                    albumImages.appendChild(imageItem);
                    
                    // 添加拖拽事件，仅为未添加的图片设置
                    if (!isAdded) {
                        setupDragEvents(imageItem);
                    }
                    
                    // 添加点击查看大图功能
                    imageItem.addEventListener('click', function(e) {
                        // 如果是拖拽操作，不触发点击事件
                        if (this.classList.contains('dragging')) return;
                        
                        // 显示大图
                        showLargeImage(img.src);
                    });
                });
            
                // 添加调试信息
                console.log('相册图片路径:', albumPhotos.map(p => p.src));
                console.log('已添加图片:', Array.from(addedImages));
            }
            
            // 设置拖拽事件
            function setupDragEvents(element) {
                element.addEventListener('dragstart', function(e) {
                    // 确保相册面板在拖拽时可见
                    if (!albumPanel.classList.contains('visible')) {
                        albumPanel.classList.add('visible');
                    }
                    
                    // 保存图片源地址到dataTransfer
                    const img = this.querySelector('img');
                    const imgSrc = img ? img.src : '';
                    
                    // 打印调试信息
                    console.log('拖拽开始，图片路径:', imgSrc);
                    
                    // 使用多种格式保存图片信息，增加兼容性
                    e.dataTransfer.setData('text/plain', imgSrc);
                    e.dataTransfer.setData('application/x-image-source', imgSrc);
                    // 保存相册ID以便更可靠地识别
                    if (this.dataset.id) {
                        e.dataTransfer.setData('application/x-album-id', this.dataset.id);
                    }
                    
                    // 添加拖拽状态类
                    this.classList.add('dragging');
                    
                    // 自定义拖拽图像（使用实际图片作为拖拽预览）
                    if (img) {
                        try {
                            const dragImg = img.cloneNode(true);
                            dragImg.style.width = '60px';
                            dragImg.style.height = '60px';
                            dragImg.style.borderRadius = '5px';
                            dragImg.style.objectFit = 'cover';
                            document.body.appendChild(dragImg);
                            
                            // 设置自定义拖拽图像
                            e.dataTransfer.setDragImage(dragImg, 30, 30);
                            
                            // 延迟删除临时元素
                            setTimeout(() => {
                                document.body.removeChild(dragImg);
                            }, 0);
                        } catch (error) {
                            console.error('设置拖拽预览图像失败:', error);
                            // 失败时使用默认拖拽图像
                        }
                    }
                    
                    // 设置允许的拖放效果
                    e.dataTransfer.effectAllowed = 'copy';
                });
                
                element.addEventListener('dragend', function(e) {
                    this.classList.remove('dragging');
                    
                    // 打印拖拽结束状态
                    console.log('拖拽结束，结果:', e.dataTransfer.dropEffect);
                    
                    // 根据拖放结果提供视觉反馈
                    if (e.dataTransfer.dropEffect === 'copy') {
                        // 拖放成功
                        this.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                            this.style.transform = '';
                        }, 300);
                    }
                });
            }
            
            // 显示/隐藏相册面板
            function toggleAlbumPanel() {
                console.log('切换相册面板可见性，当前状态:', albumPanel.classList.contains('visible'));
                
                albumPanel.classList.toggle('visible');
                
                // 如果显示相册面板，加载图片
                if (albumPanel.classList.contains('visible')) {
                    loadAlbumImages();
                }
            }
            
            // 点击上传按钮显示相册面板
            imageUploadBtn.addEventListener('click', function(e) {
                // 阻止冒泡，避免触发容器的点击事件
                e.stopPropagation();
                
                // 显示相册面板，不再基于点击位置判断
                toggleAlbumPanel();
            });
            
            // 关闭相册面板按钮点击事件
            closeAlbumBtn.addEventListener('click', function() {
                albumPanel.classList.remove('visible');
            });
            
            // 修改朋友圈显示函数，不再关闭相册面板
            function showMomentView(content, images, location, visibility, allowStrangerView) {
                const momentsView = document.querySelector('.moments-view');
                const momentContent = momentsView.querySelector('.moment-content');
                const momentImages = momentsView.querySelector('.moment-images');
                const momentLocation = momentsView.querySelector('.moment-location');
                const privacyText = momentsView.querySelector('.privacy-text');

                // 保存数据用于编辑
                momentsView.dataset.content = content;
                momentsView.dataset.location = location;
                momentsView.dataset.visibility = visibility;
                momentsView.dataset.allowStrangerView = allowStrangerView;
                
                // 保存已添加图片URL列表，用于后续恢复
                momentsView.dataset.images = JSON.stringify(images);
                
                // 设置文本内容
                momentContent.textContent = content;

                // 设置图片
                momentImages.innerHTML = '';
                if (images.length > 0) {
                    momentImages.className = `moment-images ${images.length === 1 ? 'single' : ''}`;
                    
                    // 优化图片布局
                    if (images.length === 2) {
                        momentImages.style.gridTemplateColumns = 'repeat(2, 1fr)';
                    } else if (images.length === 4) {
                        momentImages.style.gridTemplateColumns = 'repeat(2, 1fr)';
                    } else if (images.length >= 5) {
                        momentImages.style.gridTemplateColumns = 'repeat(3, 1fr)';
                    }
                    
                    // 为每个图片更新添加状态
                    images.forEach(src => {
                        const img = document.createElement('img');
                        img.src = src;
                        img.addEventListener('click', () => showLargeImage(src));
                        momentImages.appendChild(img);
                        
                        // 查找并标记相册图片为已添加
                        const matchedPhoto = findMatchingPhoto(src);
                        if (matchedPhoto) {
                            addedImages.add(matchedPhoto.id);
                            addedImages.add(matchedPhoto.src);
                            console.log(`发布朋友圈时标记图片已添加: ID=${matchedPhoto.id}`);
                        } else {
                            // 外部图片直接添加URL
                            addedImages.add(src);
                        }
                    });
                    
                    // 如果相册面板可见，刷新显示
                    if (albumPanel.classList.contains('visible')) {
                        loadAlbumImages();
                    }
                }

                // 设置位置
                if (location) {
                    momentLocation.textContent = ` · ${location}`;
                } else {
                    momentLocation.textContent = '';
                }

                // 设置可见范围
                let privacyString = '';
                const privacyMap = {
                    'public': '公开',
                    'friends': '仅朋友可见',
                    'private': '仅自己可见',
                    'custom': '部分可见'
                };
                
                privacyString = privacyMap[visibility];
                if (allowStrangerView && (visibility === 'friends' || visibility === 'custom')) {
                    privacyString += ' · 陌生人可见十条';
                }
                
                privacyText.textContent = privacyString;
                
                // 显示朋友圈界面
                momentsView.style.display = 'flex';
            }
            
            // 点击图片预览查看大图
            previewContainer.addEventListener('click', function(e) {
                const target = e.target.closest('.image-preview');
                if (!target) return;
                
                const img = target.querySelector('img');
                if (!img) return;
                
                // 创建大图预览
                const overlay = document.createElement('div');
                overlay.className = 'image-overlay';
                overlay.style.position = 'fixed';
                overlay.style.top = '0';
                overlay.style.left = '0';
                overlay.style.width = '100%';
                overlay.style.height = '100%';
                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                overlay.style.display = 'flex';
                overlay.style.alignItems = 'center';
                overlay.style.justifyContent = 'center';
                overlay.style.zIndex = '1000';
                overlay.style.opacity = '0';
                overlay.style.transition = 'opacity 0.3s';
                
                const largeImg = document.createElement('img');
                largeImg.src = img.src;
                largeImg.style.maxWidth = '90%';
                largeImg.style.maxHeight = '90%';
                largeImg.style.objectFit = 'contain';
                largeImg.style.transform = 'scale(0.9)';
                largeImg.style.transition = 'transform 0.3s';
                
                overlay.appendChild(largeImg);
                document.body.appendChild(overlay);
                
                // 添加动画
                setTimeout(() => {
                    overlay.style.opacity = '1';
                    largeImg.style.transform = 'scale(1)';
                }, 10);
                
                // 点击关闭
                overlay.addEventListener('click', function() {
                    overlay.style.opacity = '0';
                    largeImg.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 300);
                });
            });
            
            // 拖拽上传功能
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 检查是否有图片数据
                if (e.dataTransfer.types.includes('text/plain') || 
                    e.dataTransfer.types.includes('application/x-image-source') || 
                    e.dataTransfer.types.includes('application/x-album-id') ||
                    e.dataTransfer.types.includes('Files')) {
                    
                this.classList.add('drag-over');
                    // 设置放置效果为"复制"
                    e.dataTransfer.dropEffect = 'copy';
                }
            });
            
            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 只有当离开容器本身而不是子元素时才移除样式
                const rect = this.getBoundingClientRect();
                const isLeaving = 
                    e.clientX <= rect.left ||
                    e.clientX >= rect.right ||
                    e.clientY <= rect.top ||
                    e.clientY >= rect.bottom;
                
                if (isLeaving) {
                this.classList.remove('drag-over');
                }
            });
            
            container.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('drag-over');
                
                console.log('接收到拖放操作', e.dataTransfer.types);
                
                let handled = false;
                
                // 处理从相册拖拽的图片
                const imgSrc = e.dataTransfer.getData('text/plain');
                console.log('拖放的图片路径:', imgSrc);
            
                // 检查是否来自相册的图片 - 更宽松的检查条件
                const isFromAlbum = 
                    e.dataTransfer.types.includes('application/x-album-id') || 
                    imgSrc.includes('小智的相册') || 
                    (albumPhotos.some(photo => imgSrc.includes(photo.alt)));
                
                // 如果是从相册拖拽的，获取相册ID
                const albumId = e.dataTransfer.getData('application/x-album-id');
                
                // 检查是否有效图片路径
                const isValidImage = imgSrc && (
                    isFromAlbum || 
                    imgSrc.includes('data:image') || 
                    imgSrc.endsWith('.jpg') || 
                    imgSrc.endsWith('.jpeg') || 
                    imgSrc.endsWith('.png') || 
                    imgSrc.endsWith('.gif') ||
                    imgSrc.endsWith('.webp')
                );
                
                if (isValidImage) {
                    console.log('有效图片，创建预览:', isFromAlbum ? '来自相册' : '其他来源');
                    
                    // 先尝试加载图片检查是否有效
                    const testImg = new Image();
                    testImg.onload = function() {
                        // 图片加载成功，创建预览
                        createImagePreview(imgSrc);
                        checkEnablePublish();
                        // 添加成功提示动画
                        showSuccessToast('图片已添加');
                    };
                    
                    testImg.onerror = function() {
                        console.error('图片加载失败:', imgSrc);
                        // 仍然尝试创建预览，可能是路径问题但浏览器能解析
                        createImagePreview(imgSrc);
                        checkEnablePublish();
                        showSuccessToast('图片已添加 (请检查显示是否正常)');
                    };
                    
                    testImg.src = imgSrc;
                    handled = true;
                }
                
                // 如果没有处理图片URL，尝试处理文件
                if (!handled && e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    console.log('处理拖放的文件:', e.dataTransfer.files.length, '个文件');
                    
                    addImagesToPreview(e.dataTransfer.files);
                    checkEnablePublish();
                    
                    if (e.dataTransfer.files.length === 1) {
                        showSuccessToast('图片已添加');
                } else {
                        showSuccessToast(`已添加 ${e.dataTransfer.files.length} 张图片`);
                    }
                    
                    handled = true;
                }
                
                // 如果都没处理成功，显示错误信息
                if (!handled) {
                    console.error('无法处理拖放内容，不是有效的图片');
                    showErrorToast('请拖放有效的图片');
                }
            });
            
            // 添加错误提示函数
            function showErrorToast(message) {
                const toast = document.createElement('div');
                toast.className = 'error-toast';
                toast.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
                
                // 设置样式
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.left = '50%';
                toast.style.transform = 'translateX(-50%)';
                toast.style.backgroundColor = 'rgba(220, 53, 69, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '10px 20px';
                toast.style.borderRadius = '20px';
                toast.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                toast.style.zIndex = '2000';
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.3s';
                
                document.body.appendChild(toast);
                
                // 显示并自动消失
                setTimeout(() => {
                    toast.style.opacity = '1';
                }, 10);
                
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }
            
            // 添加成功提示函数
            function showSuccessToast(message) {
                const toast = document.createElement('div');
                toast.className = 'success-toast';
                toast.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                
                // 设置样式
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.left = '50%';
                toast.style.transform = 'translateX(-50%)';
                toast.style.backgroundColor = 'rgba(7, 193, 96, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '10px 20px';
                toast.style.borderRadius = '20px';
                toast.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                toast.style.zIndex = '2000';
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.3s';
                
                document.body.appendChild(toast);
                
                // 显示并自动消失
                setTimeout(() => {
                    toast.style.opacity = '1';
                }, 10);
                
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }

            // 返回按钮点击事件
            document.querySelector('.back-btn').addEventListener('click', function() {
                // 获取保存的数据，与编辑按钮的逻辑相同
                const momentsView = document.querySelector('.moments-view');
                const content = momentsView.dataset.content;
                const location = momentsView.dataset.location;
                const visibility = momentsView.dataset.visibility;
                const allowStrangerView = momentsView.dataset.allowStrangerView === 'true';
                
                // 恢复编辑表单的内容
                textarea.value = content;
                textarea.style.height = 'auto';
                textarea.style.height = textarea.scrollHeight + 'px';
                
                locationSelect.value = location;
                visibilitySelect.value = visibility;
                strangerViewToggle.checked = allowStrangerView;
                
                // 在恢复图片前，先清空已添加集合和预览容器
                addedImages.clear();
                previewContainer.innerHTML = '';
                
                // 恢复图片
                const momentImages = momentsView.querySelectorAll('.moment-images img');
                momentImages.forEach(img => {
                    createImagePreview(img.src);
                });
                
                // 启用发布按钮
                checkEnablePublish();
                
                // 如果相册面板可见，刷新显示
                if (albumPanel.classList.contains('visible')) {
                    loadAlbumImages();
                }
                
                // 显示编辑界面容器
                const editContainer = document.querySelector('.container');
                const albumPanel = document.querySelector('.album-panel');
                if (editContainer) {
                    editContainer.style.display = 'flex';
                }
                if (albumPanel && albumPanel.classList.contains('visible')) {
                    albumPanel.style.display = 'flex';
                }
                
                // 隐藏朋友圈界面
                momentsView.style.display = 'none';
            });

            // 查看大图功能
            function showLargeImage(src) {
                const overlay = document.createElement('div');
                overlay.className = 'image-overlay';
                overlay.style.position = 'fixed';
                overlay.style.top = '0';
                overlay.style.left = '0';
                overlay.style.width = '100%';
                overlay.style.height = '100%';
                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                overlay.style.display = 'flex';
                overlay.style.alignItems = 'center';
                overlay.style.justifyContent = 'center';
                overlay.style.zIndex = '3000';
                overlay.style.opacity = '0';
                overlay.style.transition = 'opacity 0.3s';
                
                // 保存之前的相册面板显示状态
                const albumPanelWasVisible = albumPanel.classList.contains('visible');

                const largeImg = document.createElement('img');
                largeImg.src = src;
                largeImg.style.maxWidth = '90%';
                largeImg.style.maxHeight = '90%';
                largeImg.style.objectFit = 'contain';
                largeImg.style.transform = 'scale(0.9)';
                largeImg.style.transition = 'transform 0.3s';
                largeImg.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                largeImg.style.borderRadius = '4px';

                const closeBtn = document.createElement('div');
                closeBtn.innerHTML = '<i class="fas fa-times"></i>';
                closeBtn.style.position = 'absolute';
                closeBtn.style.top = '20px';
                closeBtn.style.right = '20px';
                closeBtn.style.color = 'white';
                closeBtn.style.fontSize = '24px';
                closeBtn.style.cursor = 'pointer';
                closeBtn.style.zIndex = '3001';
                closeBtn.style.opacity = '0.8';
                closeBtn.style.transition = 'opacity 0.3s';
                
                closeBtn.addEventListener('mouseover', () => {
                    closeBtn.style.opacity = '1';
                });
                
                closeBtn.addEventListener('mouseout', () => {
                    closeBtn.style.opacity = '0.8';
                });

                overlay.appendChild(largeImg);
                overlay.appendChild(closeBtn);
                document.body.appendChild(overlay);

                // 添加动画
                setTimeout(() => {
                    overlay.style.opacity = '1';
                    largeImg.style.transform = 'scale(1)';
                }, 10);

                // 点击关闭
                overlay.addEventListener('click', function() {
                    overlay.style.opacity = '0';
                    largeImg.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 300);
                });
                
                // 防止点击图片本身也关闭
                largeImg.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
                
                // 点击关闭按钮
                closeBtn.addEventListener('click', function() {
                    overlay.style.opacity = '0';
                    largeImg.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 300);
                });
            }

            // 添加编辑按钮点击事件
            document.querySelector('.edit-btn').addEventListener('click', function() {
                const momentsView = document.querySelector('.moments-view');
                const content = momentsView.dataset.content;
                const location = momentsView.dataset.location;
                const visibility = momentsView.dataset.visibility;
                const allowStrangerView = momentsView.dataset.allowStrangerView === 'true';
                
                // 标记正在编辑状态
                sessionStorage.setItem('isEditing', 'true');
                
                // 恢复编辑表单的内容
                textarea.value = content;
                textarea.style.height = 'auto';
                textarea.style.height = textarea.scrollHeight + 'px';
                
                locationSelect.value = location;
                visibilitySelect.value = visibility;
                strangerViewToggle.checked = allowStrangerView;
                
                // 在恢复图片前，先清空已添加集合和预览容器
                addedImages.clear();
                previewContainer.innerHTML = '';
                
                // 恢复图片
                const momentImages = momentsView.querySelectorAll('.moment-images img');
                momentImages.forEach(img => {
                    createImagePreview(img.src);
                });
                
                // 启用发布按钮
                checkEnablePublish();
                
                // 如果相册面板可见，刷新显示
                if (albumPanel.classList.contains('visible')) {
                    loadAlbumImages();
                }
                
                // 显示编辑界面容器
                const editContainer = document.querySelector('.container');
                const albumPanel = document.querySelector('.album-panel');
                if (editContainer) {
                    editContainer.style.display = 'flex';
                }
                if (albumPanel && albumPanel.classList.contains('visible')) {
                    albumPanel.style.display = 'flex';
                }
                
                // 隐藏朋友圈界面
                momentsView.style.display = 'none';
            });

            // 添加完成按钮点击事件
            document.querySelector('.complete-btn').addEventListener('click', function() {
                // 检查是否处于编辑状态
                const isEditing = sessionStorage.getItem('isEditing') === 'true';
                
                if (isEditing) {
                    // 如果正在编辑，需要先发布编辑后的内容
                    const content = textarea.value.trim();
                    const location = locationSelect.value;
                    const visibility = visibilitySelect.value;
                    const allowStrangerView = strangerViewToggle.checked;
                    
                    // 收集已上传的图片
                    const images = [];
                    const imgElements = previewContainer.querySelectorAll('img');
                    imgElements.forEach(img => {
                        images.push(img.src);
                    });
                    
                    // 更新朋友圈界面
                    showMomentView(content, images, location, visibility, allowStrangerView);
                    
                    // 清除编辑状态
                    sessionStorage.removeItem('isEditing');
                }
                
                // 保存朋友圈数据
                saveMomentData();
                
                // 标记第二关已完成
                localStorage.setItem('level2_completed', 'true');
                
                // 添加点击动画效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    
                    // 显示成功弹窗
                    const modal = document.getElementById('success-modal');
                    modal.style.display = 'flex';
                    
                    // 3秒后跳转回主页
                    setTimeout(() => {
                        window.location.href = '../数据安全小卫士闯关乐园.html';
                    }, 3000);
                }, 200);
            });

            // 添加新华网文章链接点击事件
            document.getElementById('news-article-link').addEventListener('click', function() {
                // 跳转到指定链接
                window.open('https://mp.weixin.qq.com/s/4WVHdNpB_NmOpAg94JO0Lw', '_blank');
            });

            // 文件选择按钮点击事件
            document.getElementById('file-select-btn').addEventListener('click', function() {
                // 触发文件选择器
                imageInput.click();
            });
            
            // 确保文本框没有默认内容并自动聚焦
            setTimeout(() => {
                // 如果文本框包含不想要的默认内容，清除它
                if (textarea.value === '今天是个好日子') {
                    textarea.value = '';
                    console.log('已清除文本框中的默认内容："今天是个好日子"');
                }
                textarea.focus();
            }, 300);
            
            // 初始状态下禁用发布按钮
            publishBtn.disabled = true;
            
            // 页面加载时自动加载相册图片
            loadAlbumImages();
            
            // 首先清除不需要的默认内容
            clearUnwantedDefaultContent();
            
            // 检查是否有保存的朋友圈数据，恢复到编辑界面
            const hasSavedData = checkSavedMomentData();
            
            // 无论是否有保存的数据，都显示编辑界面
            if (hasSavedData) {
                console.log('已恢复保存的内容到编辑界面');
            } else {
                console.log('没有保存的朋友圈数据，显示空白编辑界面');
            }
            
            // 监听文本输入或图片上传，启用发布按钮
            function checkEnablePublish() {
                const hasText = textarea.value.trim() !== '';
                const hasImages = previewContainer.children.length > 0;
                const shouldEnable = hasText || hasImages;
                
                if (shouldEnable && publishBtn.disabled) {
                    publishBtn.disabled = false;
                    publishBtn.classList.add('button-enabled');
                    setTimeout(() => {
                        publishBtn.classList.remove('button-enabled');
                    }, 300);
                } else if (!shouldEnable && !publishBtn.disabled) {
                    publishBtn.disabled = true;
                }
            }
            
            textarea.addEventListener('input', checkEnablePublish);
            
            // 处理文本框自动增高
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
            
            // 处理图片上传
            imageInput.addEventListener('change', handleImageUpload);
            
            function handleImageUpload(event) {
                const files = event.target.files;
                if (!files || files.length === 0) return;
                
                addImagesToPreview(files);
                checkEnablePublish();
            }
            
            function addImagesToPreview(files) {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (!file.type.startsWith('image/')) continue;
                    
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        createImagePreview(e.target.result);
                    };
                    reader.readAsDataURL(file);
                }
            }
            
            function createImagePreview(src) {
                console.log('创建图片预览:', src);
                
                try {
                    const imagePreview = document.createElement('div');
                    imagePreview.className = 'image-preview';
                    imagePreview.style.opacity = '0';
                    imagePreview.style.transform = 'scale(0.8)';
                    
                    const img = document.createElement('img');
                    img.src = src;
                    
                    // 添加加载错误处理
                    img.onerror = function() {
                        console.error('预览图片加载失败:', src);
                        // 使用替代图像
                        this.onerror = null;
                        this.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="80" height="80"><path fill="%23999" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.12 2.12-1.41-1.41-1.41 1.41 2.83 2.83 3.54-3.54-1.43-1.41z"/></svg>';
                        // 添加错误文本显示
                        const errorDiv = document.createElement('div');
                        errorDiv.textContent = '图片加载失败';
                        errorDiv.style.position = 'absolute';
                        errorDiv.style.bottom = '5px';
                        errorDiv.style.left = '0';
                        errorDiv.style.width = '100%';
                        errorDiv.style.textAlign = 'center';
                        errorDiv.style.fontSize = '10px';
                        errorDiv.style.color = '#999';
                        errorDiv.style.background = 'rgba(255,255,255,0.8)';
                        errorDiv.style.padding = '2px 0';
                        imagePreview.appendChild(errorDiv);
                    };
                    
                    // 添加加载成功处理
                    img.onload = function() {
                        console.log('预览图片加载成功:', src);
                        
                        // 更全面的图片匹配 - 尝试多种方式匹配相册图片
                        const matchedPhoto = findMatchingPhoto(src);
                        
                        if (matchedPhoto) {
                            // 如果找到匹配的图片，将其ID添加到已添加集合中
                            addedImages.add(matchedPhoto.id);
                            addedImages.add(matchedPhoto.src);
                            console.log(`图片已标记为已添加: ID=${matchedPhoto.id}, 路径=${matchedPhoto.src}`);
                            
                            // 如果相册面板可见，刷新显示
                            if (albumPanel.classList.contains('visible')) {
                                loadAlbumImages();
                            }
                        } else {
                            // 如果是外部图片，直接添加完整URL
                            addedImages.add(src);
                            console.log('外部图片已添加:', src);
                        }
                    };
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-btn';
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 更全面的图片匹配来移除
                        const matchedPhoto = findMatchingPhoto(src);
                        
                        if (matchedPhoto) {
                            addedImages.delete(matchedPhoto.id);
                            addedImages.delete(matchedPhoto.src);
                            console.log(`图片已从已添加集合中移除: ID=${matchedPhoto.id}`);
                        } else {
                            // 如果是外部图片，移除URL
                            addedImages.delete(src);
                            console.log('外部图片已移除:', src);
                        }
                        
                        // 如果相册面板可见，刷新显示
                        if (albumPanel.classList.contains('visible')) {
                            loadAlbumImages();
                        }
                        
                        // 添加删除动画
                        imagePreview.style.opacity = '0';
                        imagePreview.style.transform = 'scale(0.8)';
                        
                        setTimeout(() => {
                            previewContainer.removeChild(imagePreview);
                            checkEnablePublish();
                        }, 200);
                    });
                    
                    imagePreview.appendChild(img);
                    imagePreview.appendChild(removeBtn);
                    previewContainer.appendChild(imagePreview);
                    
                    // 添加出现动画
                    setTimeout(() => {
                        imagePreview.style.opacity = '1';
                        imagePreview.style.transform = 'scale(1)';
                    }, 10);
                    
                    return true; // 成功创建预览
                } catch (error) {
                    console.error('创建图片预览失败:', error);
                    showErrorToast('无法创建图片预览');
                    return false; // 创建预览失败
                }
            }
            
            // 添加一个更全面的图片匹配函数
            function findMatchingPhoto(src) {
                // 首先尝试精确匹配
                let matched = albumPhotos.find(photo => photo.src === src);
                
                // 如果没找到，尝试部分匹配
                if (!matched) {
                    matched = albumPhotos.find(photo => 
                        // 检查文件名
                        src.includes(photo.src.split('/').pop()) || 
                        // 检查alt文本
                        src.includes(photo.alt) ||
                        // 检查图片名（不带扩展名）
                        src.includes(photo.src.split('/').pop().split('.')[0])
                    );
                }
                
                // 如果仍未找到，尝试更宽松的匹配
                if (!matched && src.includes('小智的相册')) {
                    // 如果路径包含相册目录，尝试提取文件名
                    const fileName = src.split('/').pop();
                    matched = albumPhotos.find(photo => 
                        photo.src.includes(fileName) || 
                        photo.alt.includes(fileName.split('.')[0])
                    );
                }
                
                return matched;
            }

            // 初始化陌生人查看选项
            const strangerViewToggle = document.getElementById('stranger-view-toggle');
            
            // 发布按钮点击事件
            publishBtn.addEventListener('click', function() {
                // 添加点击动画
                this.classList.add('button-clicked');
                setTimeout(() => {
                    this.classList.remove('button-clicked');
                }, 200);
                
                // 收集发布数据
                const content = textarea.value.trim();
                const location = locationSelect.value;
                const visibility = visibilitySelect.value;
                const allowStrangerView = strangerViewToggle.checked;
                
                // 收集已上传的图片
                const images = [];
                const imgElements = previewContainer.querySelectorAll('img');
                imgElements.forEach(img => {
                    images.push(img.src);
                });
                
                // 显示朋友圈界面并保存当前内容
                showMomentView(content, images, location, visibility, allowStrangerView);
                
                // 如果是编辑状态，清除编辑标记
                sessionStorage.removeItem('isEditing');
                
                // 重置发布表单
                resetForm();
            });
            
            // 取消按钮点击事件
            cancelBtn.addEventListener('click', function() {
                // 如果表单有内容，显示确认对话框
                const hasContent = textarea.value.trim() !== '' || previewContainer.children.length > 0;
                const hasSavedData = localStorage.getItem('savedMomentData') !== null;
                
                if (hasContent || hasSavedData) {
                    // 创建自定义确认对话框
                    const confirmDialog = document.createElement('div');
                    confirmDialog.className = 'modal';
                    confirmDialog.style.display = 'flex';
                    confirmDialog.style.zIndex = '3000';
                    
                    const dialogContent = document.createElement('div');
                    dialogContent.className = 'modal-content';
                    dialogContent.style.animation = 'modalIn 0.3s ease-out forwards';
                    dialogContent.style.maxWidth = '300px';
                    dialogContent.style.padding = '20px';
                    
                    const title = document.createElement('div');
                    title.className = 'modal-title';
                    title.style.fontSize = '18px';
                    title.style.marginBottom = '15px';
                    title.style.color = '#333';
                    title.textContent = '确定要取消发布吗？';
                    
                    const buttonsContainer = document.createElement('div');
                    buttonsContainer.style.display = 'flex';
                    buttonsContainer.style.justifyContent = 'space-between';
                    buttonsContainer.style.marginTop = '20px';
                    
                    const cancelDialogBtn = document.createElement('button');
                    cancelDialogBtn.textContent = '返回编辑';
                    cancelDialogBtn.style.padding = '8px 15px';
                    cancelDialogBtn.style.border = '1px solid #ddd';
                    cancelDialogBtn.style.borderRadius = '4px';
                    cancelDialogBtn.style.background = '#f5f5f5';
                    cancelDialogBtn.style.color = '#333';
                    cancelDialogBtn.style.cursor = 'pointer';
                    
                    const confirmBtn = document.createElement('button');
                    confirmBtn.textContent = '确定取消';
                    confirmBtn.style.padding = '8px 15px';
                    confirmBtn.style.border = 'none';
                    confirmBtn.style.borderRadius = '4px';
                    confirmBtn.style.background = '#07c160';
                    confirmBtn.style.color = 'white';
                    confirmBtn.style.cursor = 'pointer';
                    
                    const clearDataBtn = document.createElement('button');
                    clearDataBtn.textContent = '清除所有数据';
                    clearDataBtn.style.padding = '8px 15px';
                    clearDataBtn.style.border = '1px solid #ff4d4f';
                    clearDataBtn.style.borderRadius = '4px';
                    clearDataBtn.style.background = '#fff1f0';
                    clearDataBtn.style.color = '#ff4d4f';
                    clearDataBtn.style.cursor = 'pointer';
                    
                    buttonsContainer.appendChild(cancelDialogBtn);
                    buttonsContainer.appendChild(confirmBtn);
                    
                    if (hasSavedData) {
                        const noteText = document.createElement('div');
                        noteText.style.fontSize = '14px';
                        noteText.style.color = '#999';
                        noteText.style.marginTop = '10px';
                        noteText.style.marginBottom = '10px';
                        noteText.textContent = '您有保存的朋友圈数据，是否要清除？';
                        
                        dialogContent.appendChild(title);
                        dialogContent.appendChild(noteText);
                        dialogContent.appendChild(buttonsContainer);
                        dialogContent.appendChild(clearDataBtn);
                        clearDataBtn.style.marginTop = '15px';
                        clearDataBtn.style.width = '100%';
                    } else {
                        dialogContent.appendChild(title);
                        dialogContent.appendChild(buttonsContainer);
                    }
                    
                    confirmDialog.appendChild(dialogContent);
                    document.body.appendChild(confirmDialog);
                    
                    // 添加按钮事件
                    cancelDialogBtn.addEventListener('click', function() {
                        document.body.removeChild(confirmDialog);
                    });
                    
                    confirmBtn.addEventListener('click', function() {
                        document.body.removeChild(confirmDialog);
                        resetForm();
                    });
                    
                    if (hasSavedData) {
                        clearDataBtn.addEventListener('click', function() {
                            clearSavedMomentData();
                            document.body.removeChild(confirmDialog);
                            resetForm();
                            showSuccessToast('已清除所有数据');
                        });
                    }
                } else {
                    resetForm();
                }
            });
            
            // 重置表单
            function resetForm() {
                textarea.value = '';
                textarea.style.height = 'auto';
                
                // 同时清除localStorage中可能存在的默认内容
                clearUnwantedDefaultContent();
                
                // 清除图片预览
                while (previewContainer.firstChild) {
                    previewContainer.removeChild(previewContainer.firstChild);
                }
                
                // 清空已添加图片集合
                addedImages.clear();
                
                // 如果相册面板可见，刷新显示
                if (albumPanel.classList.contains('visible')) {
                    loadAlbumImages();
                }
                
                locationSelect.value = '';
                visibilitySelect.value = 'public';
                strangerViewToggle.checked = false;
                publishBtn.disabled = true;
            }

            // 清除保存的朋友圈数据
            function clearSavedMomentData() {
                try {
                    localStorage.removeItem('savedMomentData');
                    console.log('已清除保存的朋友圈数据');
                    return true;
                } catch (e) {
                    console.error('清除朋友圈数据失败:', e);
                    return false;
                }
            }
        });
        
        // 初始化AI助手
        function initAI() {
            // 引入AI助手脚本
            const aiScript = document.createElement('script');
            aiScript.src = '../ai-assistant.js';
            aiScript.onload = function() {
                const configScript = document.createElement('script');
                configScript.src = '../ai-config.js';
                configScript.onload = function() {
                    // 初始化AI助手
                    const assistant = window.initPageAIAssistant({
                        context: '朋友圈安全分享练习',
                        avatar: '../素材/3.png',
                        name: '分享小助手'
                    });
                    
                    window.aiAssistant = assistant;
                    
                    // 监听图片添加，提供安全提示和权限建议
                    const previewContainer = document.getElementById('preview-container');
                    if (previewContainer) {
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                    const addedImage = mutation.addedNodes[0];
                                    if (addedImage.classList && addedImage.classList.contains('image-preview')) {
                                        const imgSrc = addedImage.querySelector('img')?.src || '';
                                        setTimeout(() => {
                                            analyzeImageSafety(imgSrc, assistant);
                                        }, 1000);
                                    }
                                }
                            });
                        });
                        observer.observe(previewContainer, { childList: true });
                    }
                    
                    // 图片安全分析函数
                    function analyzeImageSafety(imgSrc, assistant) {
                        const visibilitySelect = document.getElementById('visibility');
                        const strangerToggle = document.getElementById('stranger-view-toggle');
                        const currentVisibility = visibilitySelect?.value || 'public';
                        const allowStranger = strangerToggle?.checked || false;
                        
                        let riskLevel = 'low';
                        let safetyTip = '';
                        let privacyRecommendation = '';
                        
                        if (imgSrc.includes('护照') || imgSrc.includes('飞机票')) {
                            riskLevel = 'high';
                            safetyTip = '⚠️ 高风险！护照和机票包含个人敏感信息（姓名、证件号、航班信息等），强烈不建议在朋友圈分享！';
                            privacyRecommendation = '建议：删除此照片，或者遮挡所有个人信息后再分享。';
                        } else if (imgSrc.includes('酒店') || imgSrc.includes('位置')) {
                            riskLevel = 'medium';
                            safetyTip = '🏨 中等风险：酒店信息可能暴露你的具体位置和行程。';
                            privacyRecommendation = '建议：设置为"仅朋友可见"，并关闭"陌生人可见十条"功能。';
                        } else if (imgSrc.includes('自拍') || imgSrc.includes('美食')) {
                            riskLevel = 'low';
                            safetyTip = '👍 低风险：这类照片相对安全，可以分享！';
                            privacyRecommendation = '建议：根据个人喜好设置隐私权限即可。';
                        } else {
                            safetyTip = '📷 请注意检查照片中是否包含个人敏感信息。';
                            privacyRecommendation = '建议：仔细检查照片内容，合理设置隐私权限。';
                        }
                        
                        // 检查当前隐私设置是否合适
                        let privacyWarning = '';
                        if (riskLevel === 'high' && currentVisibility === 'public') {
                            privacyWarning = '🚨 当前设置为"公开"，所有人都能看到！';
                        } else if (riskLevel === 'medium' && (currentVisibility === 'public' || allowStranger)) {
                            privacyWarning = '⚠️ 当前隐私设置可能不够安全。';
                        } else if (riskLevel === 'low' && currentVisibility !== 'public') {
                            privacyWarning = '✅ 当前隐私设置合适。';
                        }
                        
                        // 发送分析结果
                        assistant.addMessage(safetyTip, 'assistant');
                        if (privacyRecommendation) {
                            setTimeout(() => {
                                assistant.addMessage(privacyRecommendation, 'assistant');
                            }, 1500);
                        }
                        if (privacyWarning) {
                            setTimeout(() => {
                                assistant.addMessage(privacyWarning, 'assistant');
                            }, 2500);
                        }
                    }
                    
                    // 监听隐私设置变化
                    const visibilitySelect = document.getElementById('visibility');
                    const strangerToggle = document.getElementById('stranger-view-toggle');
                    
                    if (visibilitySelect) {
                        visibilitySelect.addEventListener('change', function() {
                            setTimeout(() => {
                                if (this.value === 'public') {
                                    assistant.addMessage('🔓 公开分享要更加谨慎，所有人都能看到你的内容！', 'assistant');
                                } else if (this.value === 'friends') {
                                    assistant.addMessage('👥 仅朋友可见是个好选择，保护了你的隐私！', 'assistant');
                                } else if (this.value === 'private') {
                                    assistant.addMessage('🔒 私密模式最安全，只有你自己能看到！', 'assistant');
                                }
                            }, 500);
                        });
                    }
                    
                    // 监听发布操作
                    const publishBtn = document.getElementById('publishBtn');
                    if (publishBtn) {
                        publishBtn.addEventListener('click', function() {
                            const imageCount = document.querySelectorAll('.image-preview').length;
                            const hasText = document.querySelector('textarea').value.trim().length > 0;
                            
                            setTimeout(() => {
                                if (imageCount > 0 || hasText) {
                                    assistant.addMessage('📱 发布前最后检查：内容是否安全？隐私设置是否合适？', 'assistant');
                                }
                            }, 500);
                        });
                    }
                    
                    // 完成任务时的祝贺
                    const completeBtn = document.querySelector('.complete-btn');
                    if (completeBtn) {
                        completeBtn.addEventListener('click', function() {
                            setTimeout(() => {
                                assistant.addMessage('🎉 恭喜你学会了安全分享！保护隐私是个好习惯！', 'assistant');
                                assistant.showNotification();
                            }, 1000);
                        });
                    }
                };
                document.head.appendChild(configScript);
            };
            document.head.appendChild(aiScript);
        }
        
        // 页面加载完成后初始化AI助手
        setTimeout(initAI, 500);
    </script>

    <!-- 添加成功弹窗 -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="modal-title">恭喜你闯关成功！</div>
            <div class="modal-text">你已成功完成"数据分享试一试"，获得一个数据防护盾！</div>
            <div class="shield-container">
                <div class="security-shield show-shield" id="security-shield"></div>
            </div>
        </div>
    </div>
</body>
</html> 